{"user_id": 2, "username": "system", "name": "System", "total_tickets": 14, "tickets": [{"id": 20, "status": "closed", "customer": {"customer_id": 1, "gender": {"gender_id": 2, "created_by": "2 system System", "updated_by": null, "name": "male", "definition": "Male Gender", "is_active": true, "created_on": "2025-06-29T20:11:10.457805+07:00", "updated_on": "2025-06-29T20:11:10.457813+07:00"}, "main_interface": {"id": 3, "created_by": "2 system System", "updated_by": null, "name": "LINE", "definition": "LINE Messaging", "is_active": true, "created_on": "2025-06-29T20:11:10.466686+07:00", "updated_on": "2025-06-29T20:11:10.466696+07:00"}, "tags": [{"id": 3, "name": "Returning", "color": "grey"}, {"id": 4, "name": "High-Value", "color": "grey"}, {"id": 6, "name": "Promotional", "color": "grey"}], "created_by": "2 system System", "updated_by": "1 admin Admin User", "universal_id": "844a5a78-ec3a-4e7d-a514-e2d26a3d31f2", "first_name": "Ftaker1", "last_name": "Ltaker1", "middle_name": null, "name": "★ Boss.SCh™", "nickname": "n1", "title": null, "picture_url": null, "date_of_birth": "2005-06-01", "age": null, "nationality": "TH", "national_id": "*********90123", "passport_number": "*********90123", "tax_id": null, "email": "<EMAIL>", "email_verified": false, "email_verified_date": null, "phone": "*********", "phone_verified": false, "phone_verified_date": null, "alternative_email": null, "alternative_phone": null, "emergency_contact_name": null, "emergency_contact_phone": null, "address": {"city": "คลองหลวง", "country": "TH", "zip_code": "12120", "address_line1": "58 หมู่ 9", "address_line2": "ถนนพหลโยธิน", "state_province_region": "ปทุมธานี"}, "subdistrict": null, "district": null, "province": null, "postal_code": null, "country": "Thailand", "career": "นักศึกษา", "occupation": null, "company_name": null, "industry": null, "annual_income_range": null, "preferred_language": "th", "preferred_contact_method": "EMAIL", "preferred_contact_time": "ANYTIME", "accepts_marketing": true, "accepts_sms": true, "accepts_email": true, "accepts_push_notifications": true, "customer_type": "PROSPECT", "customer_segment": null, "lifetime_value": null, "referral_source": null, "referral_code": null, "account_status": "ACTIVE", "risk_level": "LOW", "kyc_status": "NOT_STARTED", "kyc_verified_date": null, "first_contact_date": "2025-06-29T20:23:15+07:00", "last_contact_date": null, "last_purchase_date": null, "total_purchases": 0, "total_spent": "0.00", "linking_code": null, "linking_code_expires": null, "consent_data_processing": false, "consent_data_processing_date": null, "data_retention_period": 365, "deletion_requested": false, "deletion_requested_date": null, "notes": "", "custom_fields": {}, "created_on": "2025-06-29T20:23:15.735918+07:00", "updated_on": "2025-07-02T15:15:13.324290+07:00", "gender_id": 2, "referred_by": null, "main_interface_id": 3, "customer_tags": [3, 4, 6]}, "owner": {"id": 2, "partners": [], "departments": [], "user_tags": [], "roles": [{"id": 2, "name": "System", "definition": "System role with specific permissions"}], "created_by": null, "updated_by": null, "last_login": null, "username": "system", "email": "<EMAIL>", "date_joined": "2025-06-29T20:11:10.078707+07:00", "universal_id": "98681f1e-ab94-494e-b37e-9679aabdb4bd", "employee_id": "2", "first_name": "System", "last_name": "Bot", "middle_name": null, "name": "System", "nickname": null, "title": null, "date_of_birth": null, "gender": null, "nationality": null, "national_id": null, "picture_url": null, "work_email": "<EMAIL>", "work_phone": null, "work_phone_extension": null, "personal_email": null, "personal_phone": null, "emergency_contact_name": null, "emergency_contact_phone": null, "emergency_contact_relationship": null, "job_title": "System Bot", "position_level": null, "hire_date": null, "probation_end_date": null, "contract_end_date": null, "resignation_date": null, "last_working_date": null, "employment_type": "FULL_TIME", "status": "online", "last_active": null, "last_status_change": null, "current_workload": 0, "max_concurrent_tickets": 9999, "average_response_time": null, "languages": [], "skills": [], "certifications": [], "team": null, "performance_rating": null, "total_tickets_handled": 0, "average_satisfaction_score": null, "preferred_language": "th", "preferred_interface": "WEB", "notification_preferences": {}, "preferred_shift": "MORNING", "working_days": [], "is_active": true, "is_staff": true, "is_superuser": true, "access_level": "BASIC", "can_access_web": true, "can_access_mobile": false, "can_access_api": false, "specializations": [], "linking_code": null, "linking_code_expires": null, "notes": null, "custom_fields": {}, "created_on": "2025-06-29T20:11:10.079067+07:00", "updated_on": "2025-07-16T16:00:17.287498+07:00", "password_changed_on": null, "force_password_change": false, "reports_to": null, "groups": [], "user_permissions": []}, "topics": [], "priority": {"id": 1, "name": "Low", "description": "Low priority ticket", "level": 1, "is_active": true}, "latest_analysis": {"sentiment": "Neutral", "summary": {"thai": "บทสนทนาเกี่ยวข้องกับลูกค้าที่สอบถามเกี่ยวกับบริการประกันภัยรวมถึงการติดตามนโยบายและการสนับสนุนลูกค้าโดย Chatbot ขอข้อมูลเพิ่มเติมยืนยันรายละเอียดและสรุปด้วยการสำรวจ CSAT และข้อความขอบคุณ", "english": "The conversation involved a customer inquiring about insurance services, including policy tracking and customer support, with the chatbot requesting additional information, confirming details, and concluding with a CSAT survey and a thank you message."}, "summary_english": "The conversation involved a customer inquiring about insurance services, including policy tracking and customer support, with the chatbot requesting additional information, confirming details, and concluding with a CSAT survey and a thank you message.", "summary_thai": "บทสนทนาเกี่ยวข้องกับลูกค้าที่สอบถามเกี่ยวกับบริการประกันภัยรวมถึงการติดตามนโยบายและการสนับสนุนลูกค้าโดย Chatbot ขอข้อมูลเพิ่มเติมยืนยันรายละเอียดและสรุปด้วยการสำรวจ CSAT และข้อความขอบคุณ", "analysis_id": 47, "analyzed_at": "2025-07-08T03:51:54.585220Z", "action": "close-ticket"}, "created_by": "2 system System", "updated_by": "2 system System", "message_intents": ["Information"], "summaries": [], "llm_endpoint": "default", "feedback": {"csat": "3"}, "created_on": "2025-07-08T10:46:39.263633+07:00", "updated_on": "2025-07-08T10:51:51.912524+07:00", "customer_id": 1, "platform_identity": 1, "status_id": 6, "owner_id": 2, "ticket_interface": 3}]}